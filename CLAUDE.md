# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is a quantitative trading data service designed to be the "source of truth" for market data. The system implements a high-availability, multi-source redundant architecture for collecting, processing, and serving financial market data to downstream applications (backtesting, training, live trading).

## Architecture

The system follows a layered, service-oriented architecture:

### Core Components

1. **Ingestion Layer**: 
   - Fetcher adapters for each data source (Binance, Polygon, etc.)
   - Normalizers to convert raw data into unified canonical format
   - Pipeline: `Fetcher -> Normalizer -> Arbitration Engine`

2. **Processing & Storage Layer**:
   - Arbitration Engine: Quality scoring and source selection using priority-based fallback
   - Data validation with cross-source comparison and anomaly detection
   - Dual storage: TimescaleDB (hot data) + Parquet data lake (cold storage)

3. **Serving Layer**:
   - Unified REST/gRPC APIs for data access
   - Built-in time series processing (resampling, forward-fill)
   - Rate limiting and caching

### Data Flow

1. Scheduler triggers data collection tasks
2. Multiple fetchers collect data in parallel from different sources
3. Normalizers standardize data format
4. Arbitration engine selects best data based on quality scores
5. Data stored in both TimescaleDB (queryable) and data lake (archival)
6. Serving layer provides unified API access

## Key Design Principles

- **Multi-source redundancy**: Never depend on single data source
- **Quality-first arbitration**: Cross-validation and quality scoring
- **Standardization**: Unified format regardless of source
- **Traceability**: Full audit trail of data lineage
- **Immutability**: Raw data never modified, only new datasets created

## Database Schema

The main time series table includes both market data and rich metadata:
- Standard OHLCV fields
- `source_used`: Which data source was selected
- `quality_score`: Data quality rating
- `is_backfilled`: Whether data was gap-filled
- `raw_sources`: All sources checked during collection

## Development Notes

Since this appears to be an early-stage project with only design documentation, there are no specific build commands or testing procedures yet. The project structure suggests this will be a Python-based service given the quantitative finance context and references to TimescaleDB and Parquet storage.

When implementing:
- Follow the adapter pattern for new data sources
- Ensure all data transformations are idempotent
- Implement comprehensive monitoring and alerting
- Use configuration-as-code for data source priorities and rules